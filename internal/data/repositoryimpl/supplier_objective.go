package repositoryimpl

import (
	"context"
	"time"

	"crm/internal/biz/do"
	"crm/internal/biz/repository"
	"crm/internal/biz/vobj"
	"crm/internal/data"
	"crm/internal/data/ent"
	"crm/internal/pkg"
)

// SupplierObjectiveRepoImpl 销售目标仓储实现
type SupplierObjectiveRepoImpl struct {
	Base[ent.SupplierObjective, do.SupplierObjectiveDo, ent.SupplierObjectiveQuery]
	data *data.Data
}

// NewSupplierObjectiveRepoImpl 创建 SupplierObjectiveRepo 的实现者
func NewSupplierObjectiveRepoImpl(data *data.Data) repository.SupplierObjectiveRepo {
	return &SupplierObjectiveRepoImpl{data: data}
}

// ToEntity 转换成实体
func (s *SupplierObjectiveRepoImpl) ToEntity(po *ent.SupplierObjective) *do.SupplierObjectiveDo {
	if po == nil {
		return nil
	}
	
	entity := &do.SupplierObjectiveDo{
		ID:         po.ID,
		SupplierID: po.SupplierID,
		FromSystem: vobj.FromSystem(po.FromSystem),
	}
	
	if po.ContractStartDate != nil {
		entity.ContractStartDate = pkg.Time(*po.ContractStartDate)
	}
	if po.ContractEndDate != nil {
		entity.ContractEndDate = pkg.Time(*po.ContractEndDate)
	}
	if po.CreatedAt != nil {
		entity.CreatedAt = pkg.Time(*po.CreatedAt)
	}
	if po.UpdatedAt != nil {
		entity.UpdatedAt = pkg.Time(*po.UpdatedAt)
	}
	if !po.DeletedAt.IsZero() {
		deletedAt := pkg.Time(po.DeletedAt)
		entity.DeletedAt = &deletedAt
	}
	
	return entity
}

// CreateE 创建销售目标
func (s *SupplierObjectiveRepoImpl) CreateE(ctx context.Context, objective *do.SupplierObjectiveDo) (*do.SupplierObjectiveDo, error) {
	now := time.Now()
	
	create := s.data.GetDb(ctx).SupplierObjective.Create().
		SetSupplierID(objective.SupplierID).
		SetFromSystem(objective.FromSystem.GetValue()).
		SetContractStartDate(time.Time(objective.ContractStartDate)).
		SetContractEndDate(time.Time(objective.ContractEndDate)).
		SetCreatedAt(now).
		SetUpdatedAt(now)
	
	po, err := create.Save(ctx)
	if err != nil {
		return nil, err
	}
	
	return s.ToEntity(po), nil
}

// GetE 通过 id 获取一条数据
func (s *SupplierObjectiveRepoImpl) GetE(ctx context.Context, id int) (*do.SupplierObjectiveDo, error) {
	po, err := s.data.GetDb(ctx).SupplierObjective.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return s.ToEntity(po), nil
}

// SupplierObjectiveStageRepoImpl 销售目标阶段仓储实现
type SupplierObjectiveStageRepoImpl struct {
	Base[ent.SupplierObjectiveStage, do.SupplierObjectiveStageDo, ent.SupplierObjectiveStageQuery]
	data *data.Data
}

// NewSupplierObjectiveStageRepoImpl 创建 SupplierObjectiveStageRepo 的实现者
func NewSupplierObjectiveStageRepoImpl(data *data.Data) repository.SupplierObjectiveStageRepo {
	return &SupplierObjectiveStageRepoImpl{data: data}
}

// ToEntity 转换成实体
func (s *SupplierObjectiveStageRepoImpl) ToEntity(po *ent.SupplierObjectiveStage) *do.SupplierObjectiveStageDo {
	if po == nil {
		return nil
	}
	
	entity := &do.SupplierObjectiveStageDo{
		ID:          po.ID,
		StageType:   po.StageType,
		ObjectiveID: po.ObjectiveID,
		Amount:      po.Amount,
	}
	
	if po.StartDate != nil {
		entity.StartDate = pkg.Time(*po.StartDate)
	}
	if po.EndDate != nil {
		entity.EndDate = pkg.Time(*po.EndDate)
	}
	if po.CreatedAt != nil {
		entity.CreatedAt = pkg.Time(*po.CreatedAt)
	}
	if po.UpdatedAt != nil {
		entity.UpdatedAt = pkg.Time(*po.UpdatedAt)
	}
	if !po.DeletedAt.IsZero() {
		deletedAt := pkg.Time(po.DeletedAt)
		entity.DeletedAt = &deletedAt
	}
	
	return entity
}

// CreateBulkE 批量创建销售目标阶段
func (s *SupplierObjectiveStageRepoImpl) CreateBulkE(ctx context.Context, stages []*do.SupplierObjectiveStageDo) ([]*do.SupplierObjectiveStageDo, error) {
	if len(stages) == 0 {
		return nil, nil
	}
	
	now := time.Now()
	bulk := make([]*ent.SupplierObjectiveStageCreate, len(stages))
	
	for i, stage := range stages {
		bulk[i] = s.data.GetDb(ctx).SupplierObjectiveStage.Create().
			SetStageType(stage.StageType).
			SetObjectiveID(stage.ObjectiveID).
			SetStartDate(time.Time(stage.StartDate)).
			SetEndDate(time.Time(stage.EndDate)).
			SetAmount(stage.Amount).
			SetCreatedAt(now).
			SetUpdatedAt(now)
	}
	
	pos, err := s.data.GetDb(ctx).SupplierObjectiveStage.CreateBulk(bulk...).Save(ctx)
	if err != nil {
		return nil, err
	}
	
	result := make([]*do.SupplierObjectiveStageDo, len(pos))
	for i, po := range pos {
		result[i] = s.ToEntity(po)
	}
	
	return result, nil
}

// GetE 通过 id 获取一条数据
func (s *SupplierObjectiveStageRepoImpl) GetE(ctx context.Context, id int) (*do.SupplierObjectiveStageDo, error) {
	po, err := s.data.GetDb(ctx).SupplierObjectiveStage.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return s.ToEntity(po), nil
}

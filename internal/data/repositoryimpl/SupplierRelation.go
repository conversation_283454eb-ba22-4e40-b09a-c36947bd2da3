package repositoryimpl

import (
	"context"
	"crm/internal/biz/vobj"
	"time"

	"crm/internal/biz/bo"
	"crm/internal/biz/do"
	"crm/internal/biz/repository"
	"crm/internal/data"
	"crm/internal/data/ent"
	"crm/internal/data/ent/supplierrelation"
)

type SupplierRelationRepoImpl struct {
	Base[ent.SupplierRelation, do.SupplierRelationDo, ent.SupplierRelationQuery]
	data *data.Data
}

// NewSupplierRelationRepoImpl 创建 SupplierRelationRepo的实现者
func NewSupplierRelationRepoImpl(data *data.Data) repository.SupplierRelationRepo {
	return &SupplierRelationRepoImpl{data: data}
}

// ToEntity 转换成实体
func (s *SupplierRelationRepoImpl) ToEntity(po *ent.SupplierRelation) *do.SupplierRelationDo {
	if po == nil {
		return nil
	}
	return s.Base.ToEntity(po)
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (s *SupplierRelationRepoImpl) ToEntities(pos []*ent.SupplierRelation) []*do.SupplierRelationDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.SupplierRelationDo, len(pos))
	for k, p := range pos {
		entities[k] = s.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据，出错则 panic
func (s *SupplierRelationRepoImpl) Get(ctx context.Context, id int) *do.SupplierRelationDo {
	row := s.data.GetDb(ctx).SupplierRelation.Query().Where(supplierrelation.ID(id)).FirstX(ctx)
	return s.ToEntity(row)
}

// Find 通过多个 id 获取多条数据，出错则 panic
func (s *SupplierRelationRepoImpl) Find(ctx context.Context, ids ...int) []*do.SupplierRelationDo {
	if len(ids) == 0 {
		return nil
	}
	rows := s.data.GetDb(ctx).SupplierRelation.Query().Where(supplierrelation.IDIn(ids...)).AllX(ctx)
	return s.ToEntities(rows)
}

// Create 创建数据，出错则 panic
func (s *SupplierRelationRepoImpl) Create(ctx context.Context, createData *do.SupplierRelationDo) *do.SupplierRelationDo {
	row := s.data.GetDb(ctx).SupplierRelation.Create().
		SetSupplierID(createData.SupplierID).
		SetFromSystem(createData.FromSystem.GetValue()).
		SetRelationSupplierID(createData.RelationSupplierID).
		SetRelationFromSystem(createData.RelationFromSystem.GetValue()).
		SaveX(ctx)
	return s.ToEntity(row)
}

// CreateBulk 批量创建数据，出错则 panic
func (s *SupplierRelationRepoImpl) CreateBulk(ctx context.Context, dos []*do.SupplierRelationDo) []*do.SupplierRelationDo {
	if len(dos) == 0 {
		return nil
	}
	values := make([]*ent.SupplierRelationCreate, len(dos))
	for i, item := range dos {
		values[i] = s.data.GetDb(ctx).SupplierRelation.Create().
			SetSupplierID(item.SupplierID).
			SetFromSystem(item.FromSystem.GetValue()).
			SetRelationSupplierID(item.RelationSupplierID).
			SetRelationFromSystem(item.RelationFromSystem.GetValue())
	}
	rows := s.data.GetDb(ctx).SupplierRelation.CreateBulk(values...).SaveX(ctx)
	return s.ToEntities(rows)
}

// Update 更新数据，出错则 panic
func (s *SupplierRelationRepoImpl) Update(ctx context.Context, updateData *do.SupplierRelationDo) int {
	cnt := s.data.GetDb(ctx).SupplierRelation.Update().Where(supplierrelation.ID(updateData.ID)).
		SetSupplierID(updateData.SupplierID).
		SetFromSystem(updateData.FromSystem.GetValue()).
		SetRelationSupplierID(updateData.RelationSupplierID).
		SetRelationFromSystem(updateData.RelationFromSystem.GetValue()).
		SetRelationFinanceID(updateData.RelationFinanceID).
		SetFinanceID(updateData.FinanceID).
		SetUpdatedAt(updateData.UpdatedAt).
		SaveX(ctx)
	return cnt
}

// Delete 删除数据，出错则 panic
func (s *SupplierRelationRepoImpl) Delete(ctx context.Context, ids ...int) int {
	if len(ids) == 0 {
		return 0
	}
	//物理删除
	effectCnt := s.data.GetDb(ctx).SupplierRelation.Delete().Where(supplierrelation.IDIn(ids...)).ExecX(ctx)

	//软件删除
	// nowTime := int(time.Now().Unix())
	// deleteVal := -1
	// effectCnt := s.data.GetDb(ctx).SupplierRelation.Update().
	// 	Where(supplierrelation.IDIn(ids...), supplierrelation.StatusNEQ(deleteVal)).
	//	SetStatus(deleteVal).
	//	SetUpdateTime(nowTime).
	//	SaveX(ctx)
	return effectCnt
}

// SearchList 搜索列表，出错则 panic
func (s *SupplierRelationRepoImpl) SearchList(ctx context.Context, reqBo *bo.ReqPageBo) (dos []*do.SupplierRelationDo, respPage *bo.RespPageBo) {
	q := s.data.GetDb(ctx).SupplierRelation.Query().Where(supplierrelation.DeletedAtIsNil())
	s.SetPageByBo(q, reqBo)
	if reqBo != nil {
		respPage = s.QueryRespPage(ctx, q, reqBo)
	}
	pos := q.AllX(ctx)
	dos = s.ToEntities(pos)
	return
}

// GetE 通过 id 获取一条数据
func (s *SupplierRelationRepoImpl) GetE(ctx context.Context, id int) (*do.SupplierRelationDo, error) {
	row, err := s.data.GetDb(ctx).SupplierRelation.Query().Where(supplierrelation.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return s.ToEntity(row), nil
}

// FindE 通过多个 id 获取多条数据
func (s *SupplierRelationRepoImpl) FindE(ctx context.Context, ids ...int) ([]*do.SupplierRelationDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := s.data.GetDb(ctx).SupplierRelation.Query().Where(supplierrelation.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return s.ToEntities(rows), nil
}

// CreateE 创建数据
func (s *SupplierRelationRepoImpl) CreateE(ctx context.Context, creatData *do.SupplierRelationDo) (*do.SupplierRelationDo, error) {
	row, err := s.data.GetDb(ctx).SupplierRelation.Create().
		SetSupplierID(creatData.SupplierID).
		SetFromSystem(creatData.FromSystem.GetValue()).
		SetRelationSupplierID(creatData.RelationSupplierID).
		SetRelationFromSystem(creatData.RelationFromSystem.GetValue()).
		SetRelationFinanceID(creatData.RelationFinanceID).
		SetFinanceID(creatData.FinanceID).
		SetCreatedAt(creatData.CreatedAt).
		SetUpdatedAt(creatData.UpdatedAt).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return s.ToEntity(row), nil
}

// CreateBulkE 批量创建数据
func (s *SupplierRelationRepoImpl) CreateBulkE(ctx context.Context, dos []*do.SupplierRelationDo) ([]*do.SupplierRelationDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent.SupplierRelationCreate, len(dos))
	for i, item := range dos {
		values[i] = s.data.GetDb(ctx).SupplierRelation.Create().
			SetSupplierID(item.SupplierID).
			SetFromSystem(item.FromSystem.GetValue()).
			SetRelationSupplierID(item.RelationSupplierID).
			SetRelationFromSystem(item.RelationFromSystem.GetValue()).
			SetRelationFinanceID(item.RelationFinanceID).
			SetFinanceID(item.FinanceID).
			SetCreatedAt(item.CreatedAt).
			SetUpdatedAt(item.UpdatedAt)
	}
	rows, err := s.data.GetDb(ctx).SupplierRelation.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return s.ToEntities(rows), nil
}

// UpdateE 更新数据
func (s *SupplierRelationRepoImpl) UpdateE(ctx context.Context, updateData *do.SupplierRelationDo) (int, error) {
	cnt, err := s.data.GetDb(ctx).SupplierRelation.Update().Where(supplierrelation.ID(updateData.ID)).
		SetSupplierID(updateData.SupplierID).
		SetFromSystem(updateData.FromSystem.GetValue()).
		SetRelationSupplierID(updateData.RelationSupplierID).
		SetRelationFromSystem(updateData.RelationFromSystem.GetValue()).
		SetRelationFinanceID(updateData.RelationFinanceID).
		SetFinanceID(updateData.FinanceID).
		SetCreatedAt(updateData.CreatedAt).
		SetUpdatedAt(updateData.UpdatedAt).
		Save(ctx)
	if err != nil {
		return 0, err
	}
	return cnt, nil
}

// DeleteE 删除数据
func (s *SupplierRelationRepoImpl) DeleteE(ctx context.Context, ids ...int) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}

	// 软件删除
	return s.data.GetDb(ctx).SupplierRelation.Update().
		Where(supplierrelation.IDIn(ids...), supplierrelation.DeletedAtNotNil()).
		SetDeletedAt(time.Now()).
		Save(ctx)
}

// DeleteForceE 删除数据(硬删除)
func (s *SupplierRelationRepoImpl) DeleteForceE(ctx context.Context, ids ...int) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	// 物理删除
	return s.data.GetDb(ctx).SupplierRelation.Delete().Where(supplierrelation.IDIn(ids...)).Exec(ctx)
}

// SearchListE 搜索列表
func (s *SupplierRelationRepoImpl) SearchListE(ctx context.Context, reqBo *bo.ReqPageBo) (dos []*do.SupplierRelationDo, respPage *bo.RespPageBo, err error) {
	q := s.data.GetDb(ctx).SupplierRelation.Query().Where(
		supplierrelation.DeletedAtIsNil(),
	)
	s.SetPageByBo(q, reqBo)
	if reqBo != nil {
		respPage = s.QueryRespPage(ctx, q, reqBo)
	}
	pos, err := q.All(ctx)
	if err != nil {
		return nil, nil, err
	}
	dos = s.ToEntities(pos)
	return
}

// GetMergedList 获取已被关联的finance_id
func (s *SupplierRelationRepoImpl) GetMergedList(ctx context.Context) ([]*do.SupplierRelationDo, error) {
	pos, err := s.data.GetDb(ctx).SupplierRelation.Query().Where(
		supplierrelation.DeletedAtIsNil(),
	).All(ctx)
	return s.ToEntities(pos), err
}

// GetInfoByRelation 通过关联信息获取
func (s *SupplierRelationRepoImpl) GetInfoByRelation(ctx context.Context, relationSupplierID int, relationFromSystem vobj.FromSystem) *do.SupplierRelationDo {
	row := s.data.GetDb(ctx).SupplierRelation.Query().Where(
		supplierrelation.RelationSupplierID(relationSupplierID),
		supplierrelation.RelationFromSystem(relationFromSystem.GetValue()),
		supplierrelation.DeletedAtIsNil(),
	).FirstX(ctx)
	return s.ToEntity(row)
}

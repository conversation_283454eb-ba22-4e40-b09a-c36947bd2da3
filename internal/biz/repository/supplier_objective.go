package repository

import (
	"context"
	"crm/internal/biz/do"
)

// SupplierObjectiveRepo 销售目标仓储接口
type SupplierObjectiveRepo interface {
	// CreateE 创建销售目标
	CreateE(ctx context.Context, objective *do.SupplierObjectiveDo) (*do.SupplierObjectiveDo, error)
	
	// GetE 通过 id 获取一条数据
	GetE(ctx context.Context, id int) (*do.SupplierObjectiveDo, error)
}

// SupplierObjectiveStageRepo 销售目标阶段仓储接口
type SupplierObjectiveStageRepo interface {
	// CreateBulkE 批量创建销售目标阶段
	CreateBulkE(ctx context.Context, stages []*do.SupplierObjectiveStageDo) ([]*do.SupplierObjectiveStageDo, error)
	
	// GetE 通过 id 获取一条数据
	GetE(ctx context.Context, id int) (*do.SupplierObjectiveStageDo, error)
}

package biz

import (
	"context"
	"crm/internal/biz/vobj"
	"crm/internal/data/ent"
	"github.com/duke-git/lancet/v2/slice"

	"crm/internal/biz/bo"
	"crm/internal/biz/do"
	"crm/internal/biz/repository"
	"crm/internal/biz/rpc"
)

// SupplierBiz .
type SupplierBiz struct {
	supplierRpc               rpc.Supplier
	supplierRepo              repository.SupplierRepo
	supplierRelationRepo      repository.SupplierRelationRepo
	supplierObjectiveRepo     repository.SupplierObjectiveRepo
	supplierObjectiveStageRepo repository.SupplierObjectiveStageRepo
}

func NewSupplierBiz(supplierRepository rpc.Supplier, supplierRepo repository.SupplierRepo, supplierRelationRepo repository.SupplierRelationRepo) *SupplierBiz {
	return &SupplierBiz{supplierRpc: supplierRepository, supplierRepo: supplierRepo, supplierRelationRepo: supplierRelationRepo}
}

// GetSupplier 获取供应商详情
func (b *SupplierBiz) GetSupplier(ctx context.Context, req *bo.GetSupplierDetailBo) (*bo.SupplierDetailItem, error) {
	return b.supplierRpc.GetSupplier(ctx, req)
}

// ListSupplierSelect 获取供应商下拉选择
func (b *SupplierBiz) ListSupplierSelect(ctx context.Context, req *bo.ListSupplierSelectReqBo) *bo.ListSupplierSelectRespBo {
	dos, respPage := b.supplierRepo.SearchList(ctx, req)
	return &bo.ListSupplierSelectRespBo{
		Items: dos,
		Page:  respPage,
	}
}

// GetSupplierDo 获取供应商详情
func (b *SupplierBiz) GetSupplierDo(ctx context.Context, req *bo.GetSupplierDetailBo) (*do.SupplierDo, error) {
	return b.supplierRepo.GetE(ctx, req.ID, req.Tag)
}

// ListSupplier 获取供应商列表
func (b *SupplierBiz) ListSupplier(ctx context.Context, req *bo.ListSupplierReqBo) (*bo.ListSupplierRespBo, error) {
	return b.supplierRpc.ListSupplier(ctx, req)
}

// MergedList 获取供应商列表
func (b *SupplierBiz) MergedList(ctx context.Context, pageBo *bo.ReqPageBo) (*bo.RespMergeSupplier, error) {
	reqList := &bo.ListSupplierSelectReqBo{
		Page: pageBo,
		FromSystems: []vobj.FromSystem{
			vobj.FromSystemDirect,
			vobj.FromSystemCard,
		},
	}
	// 获取已被合并的供应商
	mergedList, err := b.supplierRelationRepo.GetMergedList(ctx)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}

	// 排除已
	reqList.ExcludeIds = slice.Map(mergedList, func(_ int, item *do.SupplierRelationDo) int {
		return item.RelationFinanceID
	})
	resp := &bo.RespMergeSupplier{
		Relations: mergedList,
	}
	resp.List, resp.Page = b.supplierRepo.SearchList(ctx, reqList)
	return resp, nil
}

// GetListByIds 获取多个供应商
func (b *SupplierBiz) GetListByIds(ctx context.Context, ids ...int) []*do.SupplierDo {
	return b.supplierRepo.FindByIds(ctx, ids...)
}

// GetInfoBySupplierId 获取供应商详情
func (b *SupplierBiz) GetInfoBySupplierId(ctx context.Context, supplierId int, tag vobj.FromSystem) (*do.SupplierDo, error) {
	info := b.supplierRepo.GetInfoBySupplierId(ctx, supplierId, tag)
	return info, nil
}

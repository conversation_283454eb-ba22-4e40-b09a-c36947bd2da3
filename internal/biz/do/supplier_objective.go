package do

import (
	"crm/internal/biz/vobj"
	"crm/internal/pkg"
)

// SupplierObjectiveDo 销售目标 DO
type SupplierObjectiveDo struct {
	ID                int
	SupplierID        int
	FromSystem        vobj.FromSystem
	ContractStartDate pkg.Time
	ContractEndDate   pkg.Time
	CreatedAt         pkg.Time
	UpdatedAt         pkg.Time
	DeletedAt         *pkg.Time
}

// SupplierObjectiveStageDo 销售目标阶段 DO
type SupplierObjectiveStageDo struct {
	ID          int
	StageType   int
	ObjectiveID int
	StartDate   pkg.Time
	EndDate     pkg.Time
	Amount      int // 目标金额，单位：分
	CreatedAt   pkg.Time
	UpdatedAt   pkg.Time
	DeletedAt   *pkg.Time
}

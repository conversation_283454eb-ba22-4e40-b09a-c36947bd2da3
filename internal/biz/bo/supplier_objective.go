package bo

import (
	"crm/internal/biz/vobj"
	"crm/internal/pkg"
)

// AddSupplierObjectiveReqBo 添加销售目标请求 BO
type AddSupplierObjectiveReqBo struct {
	SupplierID        int
	FromSystem        vobj.FromSystem
	ContractStartDate pkg.Time
	ContractEndDate   pkg.Time
	Stages            []*AddSupplierObjectiveStageReqBo
}

// AddSupplierObjectiveStageReqBo 添加销售目标阶段请求 BO
type AddSupplierObjectiveStageReqBo struct {
	StageType int       // 阶段类型：1-一季度，2-二季度，3-三季度，4-四季度
	StartDate pkg.Time  // 开始日期
	EndDate   pkg.Time  // 结束日期
	Amount    float64   // 目标金额
}

// AddSupplierObjectiveRespBo 添加销售目标响应 BO
type AddSupplierObjectiveRespBo struct {
	ID int // 目标ID
}

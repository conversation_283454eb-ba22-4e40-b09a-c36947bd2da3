package service

import (
	"context"
	"fmt"
	"sort"
	"time"

	"crm/api"
	pb "crm/api/inner"
	"crm/internal/biz"
	"crm/internal/biz/bo"
	"crm/internal/biz/do"
	"crm/internal/biz/vobj"
	"crm/internal/conf"
	"crm/internal/pkg/slices"
	"crm/internal/service/build"

	"github.com/duke-git/lancet/v2/slice"
)

type SupplierService struct {
	pb.UnimplementedSupplierServer

	c *conf.Bootstrap

	supplierBiz *biz.SupplierBiz
}

func NewSupplierService(c *conf.Bootstrap, supplierBiz *biz.SupplierBiz) pb.SupplierHTTPServer {
	return &SupplierService{
		supplierBiz: supplierBiz,
		c:           c,
	}
}

// GetSupplier 上游详情
func (s *SupplierService) GetSupplier(ctx context.Context, req *pb.GetSupplierRequest) (*pb.GetSupplierReply, error) {
	params := &bo.GetSupplierDetailBo{
		ID:  int(req.GetId()),
		Tag: vobj.FromSystem(req.GetTag()),
	}
	detail, err := s.supplierBiz.GetSupplier(ctx, params)
	if err != nil {
		return nil, err
	}
	return &pb.GetSupplierReply{
		Detail: build.SupplierBuilder(detail),
	}, nil
}

// ListSupplier 上游列表
func (s *SupplierService) ListSupplier(ctx context.Context, req *pb.ListSupplierRequest) (*pb.ListSupplierReply, error) {
	params := &bo.ListSupplierReqBo{
		Keyword: req.GetKeyword(),
		Page:    build.NewPageReq(req.GetPage()),
		Tag:     vobj.FromSystem(req.GetTag()),
	}
	listReply, err := s.supplierBiz.ListSupplier(ctx, params)
	if err != nil {
		return nil, err
	}
	return &pb.ListSupplierReply{
		List: build.SupplierListBuilder(listReply.Items...),
		Page: build.NewPageResp(listReply.Page),
	}, nil
}

// ListSupplierSystem 获取上游系统列表
func (s *SupplierService) ListSupplierSystem(_ context.Context, _ *pb.ListSupplierSystemRequest) (*pb.ListSupplierSystemReply, error) {
	list := slices.To(s.c.GetSupplierSystem(), func(item int32) (*api.SelectOption, bool) {
		return &api.SelectOption{
			Label: vobj.FromSystem(item).Name(),
			Value: item,
		}, true
	})
	return &pb.ListSupplierSystemReply{
		List: list,
	}, nil
}

// ListSupplierSelect 获取供应商下拉选择
func (s *SupplierService) ListSupplierSelect(ctx context.Context, req *pb.ListSupplierSelectRequest) (*pb.ListSupplierSelectReply, error) {
	params := &bo.ListSupplierSelectReqBo{
		Page:    build.NewPageReq(req.GetPage()),
		Keyword: req.GetKeyword(),
		Status:  vobj.SupplierStatus(req.GetStatus()),
	}
	if req.Tag > 0 {
		params.FromSystems = []vobj.FromSystem{vobj.FromSystem(req.Tag)}
	}
	ret := &pb.ListSupplierSelectReply{
		List:  make([]*api.SupplierSelectItem, 0),
		Total: 0,
	}
	listSupplierSelectRespBo := s.supplierBiz.ListSupplierSelect(ctx, params)
	if listSupplierSelectRespBo == nil || len(listSupplierSelectRespBo.Items) < 1 || listSupplierSelectRespBo.Page == nil {
		return ret, nil
	}
	return &pb.ListSupplierSelectReply{
		List:  build.SupplierSelectListBuilder(listSupplierSelectRespBo.Items...),
		Total: uint32(listSupplierSelectRespBo.Page.Total),
	}, nil
}

// ListSupplierTree 获取供应商下拉选择
func (s *SupplierService) ListSupplierTree(ctx context.Context, req *pb.ListSupplierTreeRequest) (*pb.ListSupplierTreeReply, error) {
	listSupplierSelectRespBo := s.supplierBiz.ListSupplierSelect(ctx, &bo.ListSupplierSelectReqBo{
		Keyword: req.GetKeyword(),
		Status:  vobj.SupplierStatus(req.GetStatus()),
	})
	ret := &pb.ListSupplierTreeReply{
		List: make([]*api.SupplierTreeItem, 0),
	}
	if listSupplierSelectRespBo == nil || len(listSupplierSelectRespBo.Items) < 1 {
		return ret, nil
	}
	orderTypes := make([]int, 0)
	listMap := make(map[vobj.FromSystem][]*do.SupplierDo)
	for _, val := range listSupplierSelectRespBo.Items {
		if _, ok := listMap[val.FromSystem]; !ok {
			listMap[val.FromSystem] = make([]*do.SupplierDo, 0)
			orderTypes = append(orderTypes, val.FromSystem.GetValue())
		}
		listMap[val.FromSystem] = append(listMap[val.FromSystem], val)
	}
	sort.Ints(orderTypes)
	for i, orderType := range orderTypes {
		orderTyp := vobj.FromSystem(orderType)
		ret.List = append(ret.List, &api.SupplierTreeItem{
			Key:      fmt.Sprintf("%d", orderType),
			Value:    uint32(orderType),
			Title:    orderTyp.Name(),
			Disabled: false,
			Children: make([]*api.SupplierTreeItem, 0),
			Tag:      int32(orderType),
		})
		for _, dos := range listMap[vobj.FromSystem(orderType)] {
			item := &api.SupplierTreeItem{
				Key:      fmt.Sprintf("%d_%d", orderType, dos.SupplierID),
				Value:    uint32(dos.SupplierID),
				Title:    dos.SupplierName,
				Disabled: dos.Status.IsDisable(),
				Children: nil,
				Tag:      int32(orderType),
			}
			if req.Status != nil && req.GetStatus() == 0 {
				item.Disabled = false
			}
			ret.List[i].Children = append(ret.List[i].Children, item)
		}
	}
	return ret, nil
}

// MergedList 销售目标的上游列表
func (s *SupplierService) MergedList(ctx context.Context, req *pb.MergedListRequest) (*pb.MergedListReply, error) {
	pageBo := &bo.ReqPageBo{
		Num:  int(req.GetPage().GetPage()),
		Size: int(req.GetPage().GetPageSize()),
	}
	// 获取合并列表
	mergeList, err := s.supplierBiz.MergedList(ctx, pageBo)
	if err != nil {
		return nil, err
	}

	// 获取被组合的上游
	relationFinanceIds := slice.Map(mergeList.Relations, func(_ int, item *do.SupplierRelationDo) int {
		return item.RelationFinanceID
	})
	relationSupplierDos := s.supplierBiz.GetListByIds(ctx, relationFinanceIds...)
	relationSupplierMap := slice.KeyBy(relationSupplierDos, func(item *do.SupplierDo) int {
		return item.ID
	})

	// 拼装关系
	relationsMap := slice.GroupWith(mergeList.Relations, func(item *do.SupplierRelationDo) int {
		return item.FinanceID
	})
	resp := &pb.MergedListReply{
		List: make([]*pb.MergedListReplyDetail, 0, len(mergeList.List)),
		Page: &api.PageReply{
			Total:    int32(mergeList.Page.Total),
			Page:     int32(mergeList.Page.Num),
			PageSize: int32(mergeList.Page.Size),
		},
	}
	for _, row := range mergeList.List {
		item := &pb.MergedListReplyDetail{
			SupplierId:   int32(row.SupplierID),
			FinanceId:    int32(row.ID),
			SupplierName: row.SupplierName,
			FromSystem:   row.FromSystem.String(),
			Status: &api.EnumItem{
				Value: int32(row.Status),
				Label: row.Status.String(),
			},
		}
		for _, relation := range relationsMap[row.ID] {
			item.Relations = append(item.Relations, &pb.MergedListReplyDetailRelation{
				FinanceId:  int32(relation.RelationFinanceID),
				FromSystem: relation.RelationFromSystem.String(),
				SupplierId: int32(relation.RelationSupplierID),
				CreatedAt:  relation.CreatedAt.Format(time.DateTime),
				SupplierName: func() string {
					if relationSupplier, isOk := relationSupplierMap[relation.RelationFinanceID]; isOk {
						return relationSupplier.SupplierName
					}
					return ""
				}(),
			})
		}
		resp.List = append(resp.List, item)
	}
	return resp, nil
}

// AddObjective 添加销售目标
func (s *SupplierService) AddObjective(ctx context.Context, req *pb.AddObjectiveRequest) (*pb.AddObjectiveReply, error) {
	// 参数转换
	reqBo := &bo.AddSupplierObjectiveReqBo{
		SupplierID:        int(req.GetSupplierId()),
		FromSystem:        vobj.FromSystem(req.GetFromSystem()),
		ContractStartDate: pkg.ParseTime(req.GetContractStartDate()),
		ContractEndDate:   pkg.ParseTime(req.GetContractEndDate()),
		Stages:            make([]*bo.AddSupplierObjectiveStageReqBo, len(req.GetStages())),
	}

	for i, stage := range req.GetStages() {
		reqBo.Stages[i] = &bo.AddSupplierObjectiveStageReqBo{
			StageType: int(stage.GetStageType()),
			StartDate: pkg.ParseTime(stage.GetStartDate()),
			EndDate:   pkg.ParseTime(stage.GetEndDate()),
			Amount:    stage.GetAmount(),
		}
	}

	// 调用业务逻辑
	respBo, err := s.supplierBiz.AddObjective(ctx, reqBo)
	if err != nil {
		return nil, err
	}

	return &pb.AddObjectiveReply{
		Id: int32(respBo.ID),
	}, nil
}

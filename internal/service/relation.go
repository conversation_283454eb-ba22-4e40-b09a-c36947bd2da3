package service

import (
	"context"
	pb "crm/api/inner"
	"crm/api/myerr"
	"crm/internal/biz"
	"crm/internal/biz/do"
	"crm/internal/biz/vobj"
	"crm/internal/conf"
	"crm/internal/data/ent"
	"strings"
)

type RelationService struct {
	pb.UnimplementedRelationServer

	c *conf.Bootstrap

	supplierBiz *biz.SupplierBiz
	relationBiz *biz.RelationBiz
}

func NewRelationService(c *conf.Bootstrap, supplierBiz *biz.SupplierBiz, relationBiz *biz.RelationBiz) pb.RelationHTTPServer {
	return &RelationService{
		supplierBiz: supplierBiz,
		relationBiz: relationBiz,
		c:           c,
	}
}

// Add 添加关联关系
func (r *RelationService) Add(ctx context.Context, req *pb.RelationAddRequest) (*pb.RelationAddReply, error) {
	system := vobj.FromSystem(req.Tag)
	if !system.IsCard() {
		return nil, myerr.ErrorNotAllow("当前仅支持关联%s的上游", vobj.FromSystemCard.Name())
	}
	// 获取关联的finance表信息
	supplierList := r.supplierBiz.GetListByIds(ctx, int(req.FinanceId))
	if len(supplierList) == 0 {
		return nil, myerr.ErrorNotFound("上游不存在不存在，fid=%d", req.FinanceId)
	}

	// 获取被关联的finance表信息
	relationSupplierInfo, err := r.supplierBiz.GetInfoBySupplierId(ctx, int(req.SupplierId), system)
	if ent.IsNotFound(err) || relationSupplierInfo == nil {
		return nil, myerr.ErrorNotFound("关联的上游数据不存在，请确认或稍候重试")
	}
	if err != nil {
		return nil, err
	}

	// 检查是否已被关联
	relationRecord := r.relationBiz.GetInfoByRelation(ctx, relationSupplierInfo.SupplierID, system)
	if relationRecord != nil {
		return nil, myerr.ErrorNotAllow("该上游已被关联到%d上游下，请务重复关联", relationRecord.SupplierID)
	}

	// 创建关联
	relationDo, err := r.relationBiz.Add(ctx, &do.SupplierRelationDo{
		FinanceID:          int(req.FinanceId),
		SupplierID:         supplierList[0].SupplierID,
		FromSystem:         vobj.FromSystemDirect,
		RelationFromSystem: system,
		RelationSupplierID: int(req.SupplierId),
		RelationFinanceID:  relationSupplierInfo.ID,
	})
	if err != nil && strings.Contains(err.Error(), "uq_relation") {
		return nil, myerr.ErrorNotAllow("该上游已被关联，请确认或重试")
	}
	if err != nil {
		return nil, err
	}
	return &pb.RelationAddReply{Id: int32(relationDo.ID)}, nil
}
